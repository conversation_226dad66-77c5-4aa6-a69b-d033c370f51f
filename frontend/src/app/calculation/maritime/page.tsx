'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { calculationAPI, paymentAPI } from '@/lib/api';

interface DropdownOption {
  value: string;
  label: string;
}

interface Port {
  name: string;
  location: string;
}

interface MaritimeCalculationData {
  vesselName: string;
  grt: number;
  dwt: number;
  loa: number;
  beam: number;
  draft: number;
  portOfLoading: string;
  portOfDischarge: string;
  cargoType: string;
  cargoWeight: number;
  isDangerous: boolean;
  currency: string;
  portName?: string;
  portCallType?: string;
  vesselType?: string;
  flagCategory?: string;
  portLocation?: string;
  grossRegisterTonnage?: number;
  netRegisterTonnage?: number;
  daysAtQuay?: number;
  usdTryRate?: number;
  eurUsdRate?: number;
  cargoCategory?: string;
  cargoUnits?: number;
  cargoNature?: string;
  manualExchangeRate?: boolean;
}

interface CalculationResult {
  pilotage?: {
    calculation: string;
    total: number;
  };
  tugboat?: {
    calculation: string;
    total: number;
  };
  portDues?: {
    calculation: string;
    total: number;
  };
  agency?: {
    calculation: string;
    total: number;
  };
  total?: number;
  currency?: string;
  fullResult?: any;
}

export default function MaritimeCalculation() {
  const [formData, setFormData] = useState<MaritimeCalculationData>({
    vesselName: '',
    grt: 0,
    dwt: 0,
    loa: 0,
    beam: 0,
    draft: 0,
    portOfLoading: '',
    portOfDischarge: '',
    cargoType: '',
    cargoWeight: 0,
    isDangerous: false,
    currency: 'USD',
    portName: '',
    portCallType: '',
    vesselType: '',
    flagCategory: '',
    portLocation: '',
    grossRegisterTonnage: 0,
    netRegisterTonnage: 0,
    daysAtQuay: 1,
    usdTryRate: 30.0,
    eurUsdRate: 1.1,
    cargoCategory: '',
    cargoUnits: 0,
    cargoNature: '',
    manualExchangeRate: false
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const router = useRouter();

  const [ports, setPorts] = useState<Port[]>([]);
  const [vesselTypes, setVesselTypes] = useState<DropdownOption[]>([]);
  const [cargoTypes, setCargoTypes] = useState<DropdownOption[]>([]);
  const [cargoCategories, setCargoCategories] = useState<DropdownOption[]>([]);
  const [portCallTypes, setPortCallTypes] = useState<DropdownOption[]>([]);
  const [flagCategories, setFlagCategories] = useState<DropdownOption[]>([]);
  const [exchangeRates, setExchangeRates] = useState<any>(null);

  useEffect(() => {
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (!token) {
      router.push('/login');
      return;
    }

    const loadDropdownData = async () => {
      try {
        const [portsRes, vesselTypesRes, cargoTypesRes, cargoCategoriesRes, portCallTypesRes, flagCategoriesRes, exchangeRatesRes] = await Promise.all([
          calculationAPI.getPortSuggestions(),
          calculationAPI.getVesselTypes(),
          calculationAPI.getCargoTypes(),
          calculationAPI.getCargoCategories(),
          calculationAPI.getPortCallTypes(),
          calculationAPI.getFlagCategories(),
          calculationAPI.getExchangeRates()
        ]);

        setPorts(portsRes.data);
        setVesselTypes(vesselTypesRes.data);
        setCargoTypes(cargoTypesRes.data);
        setCargoCategories(cargoCategoriesRes.data);
        setPortCallTypes(portCallTypesRes.data);
        setFlagCategories(flagCategoriesRes.data);

        if (exchangeRatesRes.success) {
          setExchangeRates(exchangeRatesRes.data);
          setFormData(prev => ({
            ...prev,
            usdTryRate: exchangeRatesRes.data.usdTry,
            eurUsdRate: exchangeRatesRes.data.eurUsd
          }));
        }
      } catch (error: any) {
        console.error('Failed to load dropdown data:', error);
        // Show user-friendly error message
        setError(`Failed to load form data: ${error.message || 'Please try refreshing the page'}`);
      }
    };

    loadDropdownData();
  }, [router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked :
                     type === 'number' ? parseFloat(value) || 0 : value;

    // Special handling for port selection
    let additionalUpdates = {};
    if (name === 'portName') {
      if (value) {
        // Find the selected port and auto-populate the location
        const selectedPort = ports.find(port => port.name === value);
        if (selectedPort) {
          additionalUpdates = { portLocation: selectedPort.location };
        }
      } else {
        // If port is cleared, clear the location as well
        additionalUpdates = { portLocation: '' };
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: newValue,
      ...(name === 'grt' && { grossRegisterTonnage: newValue as number }),
      ...(name === 'grossRegisterTonnage' && { grt: newValue as number }),
      ...additionalUpdates
    }));
  };

  const handleCalculate = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const backendData = {
        portName: formData.portName || 'Istanbul',
        portLocation: formData.portLocation || 'Istanbul',
        portCallType: formData.portCallType || 'simple-port-call',
        vesselType: formData.vesselType || 'other',
        flagCategory: formData.flagCategory || 'foreign',
        grossRegisterTonnage: formData.grossRegisterTonnage || formData.grt,
        netRegisterTonnage: formData.netRegisterTonnage || Math.round(formData.grt * 0.7),
        deadweightTonnage: formData.dwt,
        daysAtQuay: formData.daysAtQuay || 1,
        cargoType: formData.cargoType || 'general',
        cargoQuantity: formData.cargoWeight,
        cargoUnits: formData.cargoUnits,
        cargoCategory: formData.cargoCategory || 'general',
        cargoNature: formData.cargoNature || 'General Cargo',
        usdTryRate: formData.usdTryRate || 30.0,
        eurUsdRate: formData.eurUsdRate || 1.1,
        isDangerous: formData.isDangerous || false
      };

      const response = await calculationAPI.calculateMaritime(backendData);
      const fullResult = response.data;
      
      const simplifiedResult: CalculationResult = {
        pilotage: fullResult.fees?.pilotage ? {
          calculation: fullResult.fees.pilotage.calculation || '',
          total: fullResult.fees.pilotage.total || 0
        } : undefined,
        tugboat: fullResult.fees?.tugboat ? {
          calculation: fullResult.fees.tugboat.calculation || '',
          total: fullResult.fees.tugboat.total || 0
        } : undefined,
        portDues: fullResult.fees?.quayDue ? {
          calculation: fullResult.fees.quayDue.calculation || '',
          total: fullResult.fees.quayDue.total || 0
        } : undefined,
        agency: fullResult.fees?.agencyFee ? {
          calculation: fullResult.fees.agencyFee.calculation || '',
          total: fullResult.fees.agencyFee.usdAmount || 0
        } : undefined,
        total: fullResult.grandTotal || 0,
        currency: fullResult.currency || 'USD',
        fullResult: fullResult
      };

      setResult(simplifiedResult);
    } catch (err: any) {
      console.error('Calculation error:', err);
      setError(err.message || 'Calculation failed');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!result || !result.total) return;
    setPaymentLoading(true);
    try {
      const calculationId = `temp_${Date.now()}`;
      const paymentResponse = await paymentAPI.initializeProformaPayment(
        calculationId,
        result.total,
        result.currency || 'USD'
      );
      if (paymentResponse.success && paymentResponse.data.paymentPageUrl) {
        window.open(paymentResponse.data.paymentPageUrl, '_blank');
      } else {
        alert('Payment initialization failed');
      }
    } catch (err: any) {
      console.error('Payment error:', err);
      alert('Failed to initialize payment: ' + (err.message || 'Unknown error'));
    } finally {
      setPaymentLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!result || !result.fullResult) return;
    setPdfLoading(true);
    try {
      const blob = await calculationAPI.generatePDF(result.fullResult, formData.vesselName);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const portName = formData.portName || formData.portOfLoading || 'Port';
      const date = new Date().toISOString().split('T')[0];
      link.download = `Proforma_${portName}_${date}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('PDF download error:', err);
      alert('Failed to download PDF: ' + (err.message || 'Unknown error'));
    } finally {
      setPdfLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-500 text-sm">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                Maritime Proforma Calculation
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                  Vessel Calculation Form
                </h3>

                {error && (
                  <div className="mb-4 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 p-4">
                    <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                  </div>
                )}

                <form onSubmit={handleCalculate} className="space-y-8">
                  {/* Exchange Rates Display */}
                  {exchangeRates && (
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
                        Current Official Exchange Rates (TCMB)
                      </h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">USD/TRY:</span>
                          <span className="ml-2 font-semibold text-blue-600 dark:text-blue-400">
                            {exchangeRates.usdTry?.toFixed(4)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">EUR/USD:</span>
                          <span className="ml-2 font-semibold text-blue-600 dark:text-blue-400">
                            {exchangeRates.eurUsd?.toFixed(4)}
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        Source: {exchangeRates.source} | Last updated: {exchangeRates.date}
                      </p>
                    </div>
                  )}

                  {/* Port and Ship Information Section */}
                  <div className="space-y-4">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Port and Ship Information
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Basic vessel and port details
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Vessel Name *
                        </label>
                        <input
                          type="text"
                          name="vesselName"
                          value={formData.vesselName}
                          onChange={handleInputChange}
                          placeholder="Enter vessel name"
                          required
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Port *
                        </label>
                        <select
                          name="portName"
                          value={formData.portName || ''}
                          onChange={handleInputChange}
                          required
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Port</option>
                          {ports.map((port, index) => (
                            <option key={index} value={port.name}>
                              {port.name} - {port.location}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Post Location
                        </label>
                        <input
                          type="text"
                          name="portLocation"
                          value={formData.portLocation || ''}
                          onChange={handleInputChange}
                          placeholder="Port location"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Port Call Type
                        </label>
                        <select
                          name="portCallType"
                          value={formData.portCallType || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Port Call Type</option>
                          {portCallTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Vessel Type *
                        </label>
                        <select
                          name="vesselType"
                          value={formData.vesselType || ''}
                          onChange={handleInputChange}
                          required
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Vessel Type</option>
                          {vesselTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Flag Category *
                        </label>
                        <select
                          name="flagCategory"
                          value={formData.flagCategory || ''}
                          onChange={handleInputChange}
                          required
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Flag Category</option>
                          {flagCategories.map((category) => (
                            <option key={category.value} value={category.value}>
                              {category.label}
                            </option>
                          ))}
                        </select>
                      </div>

                    </div>
                  </div>

                  {/* Tonnage Information Section */}
                  <div className="space-y-4">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Tonnage Information
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Vessel tonnage specifications and port stay details
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Gross Register Tonnage (GRT) *
                        </label>
                        <input
                          type="number"
                          name="grt"
                          value={formData.grt}
                          onChange={handleInputChange}
                          placeholder="Enter GRT"
                          required
                          min="0"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Net Register Tonnage (NRT) *
                        </label>
                        <input
                          type="number"
                          name="netRegisterTonnage"
                          value={formData.netRegisterTonnage || 0}
                          onChange={handleInputChange}
                          placeholder="Enter NRT"
                          required
                          min="0"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Deadweight Tonnage (DWT)
                        </label>
                        <input
                          type="number"
                          name="dwt"
                          value={formData.dwt}
                          onChange={handleInputChange}
                          placeholder="Enter DWT"
                          min="0"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Port Stay Duration (Days) *
                        </label>
                        <input
                          type="number"
                          name="daysAtQuay"
                          value={formData.daysAtQuay || 1}
                          onChange={handleInputChange}
                          required
                          min="1"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Cargo Information Section */}
                  <div className="space-y-4">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Cargo Information
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Details about the cargo being transported
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cargo Type
                        </label>
                        <select
                          name="cargoType"
                          value={formData.cargoType}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Cargo Type</option>
                          {cargoTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cargo Category
                        </label>
                        <select
                          name="cargoCategory"
                          value={formData.cargoCategory || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select Cargo Category</option>
                          {cargoCategories.map((category) => (
                            <option key={category.value} value={category.value}>
                              {category.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cargo Weight (MT)
                        </label>
                        <input
                          type="number"
                          name="cargoWeight"
                          value={formData.cargoWeight}
                          onChange={handleInputChange}
                          placeholder="Enter cargo weight in metric tons"
                          min="0"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cargo Units (TEU/Vehicle Count)
                        </label>
                        <input
                          type="number"
                          name="cargoUnits"
                          value={formData.cargoUnits || 0}
                          onChange={handleInputChange}
                          placeholder="TEU for containers, count for vehicles"
                          min="0"
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Cargo Nature/Details
                        </label>
                        <textarea
                          name="cargoNature"
                          value={formData.cargoNature || ''}
                          onChange={handleInputChange}
                          placeholder="Detailed description of cargo nature"
                          rows={3}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Exchange Rates Section */}
                  <div className="space-y-4">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Exchange Rates
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Currency exchange rates for calculation
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center md:col-span-2">
                        <input
                          type="checkbox"
                          name="manualExchangeRate"
                          checked={formData.manualExchangeRate}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Use manual exchange rate (override official rates)
                        </label>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          USD/TRY Rate *
                        </label>
                        <input
                          type="number"
                          name="usdTryRate"
                          value={formData.usdTryRate || 0}
                          onChange={handleInputChange}
                          step="0.0001"
                          min="0"
                          disabled={!formData.manualExchangeRate}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:bg-gray-100 disabled:text-gray-500"
                        />
                        {exchangeRates && (
                          <p className="text-xs text-gray-500 mt-1">
                            Current official rate: {exchangeRates.usdTry?.toFixed(4)} (Source: {exchangeRates.source})
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          EUR/USD Rate
                        </label>
                        <input
                          type="number"
                          name="eurUsdRate"
                          value={formData.eurUsdRate || 0}
                          onChange={handleInputChange}
                          step="0.0001"
                          min="0"
                          disabled={!formData.manualExchangeRate}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:bg-gray-100 disabled:text-gray-500"
                        />
                        {exchangeRates && (
                          <p className="text-xs text-gray-500 mt-1">
                            Current official rate: {exchangeRates.eurUsd?.toFixed(4)} (Source: {exchangeRates.source})
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Special Situations Section */}
                  <div className="space-y-4">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Special Situations
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Special conditions and additional requirements
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="isDangerous"
                          checked={formData.isDangerous}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Dangerous Cargo (30% surcharge)
                        </label>
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Special Conditions and Notes
                        </label>
                        <textarea
                          name="specialConditions"
                          placeholder="Enter special conditions, one per line..."
                          rows={3}
                          className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <button
                      type="button"
                      className="flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Reset
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Calculating...' : 'Calculate'}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {result && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                      Calculation Results
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleDownloadPDF}
                        disabled={pdfLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {pdfLoading ? 'Generating...' : 'Download PDF'}
                      </button>
                      <button
                        onClick={handlePayment}
                        disabled={paymentLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {paymentLoading ? 'Processing...' : 'Pay Now'}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {result.total && (
                      <div className="border-t pt-4 mt-4">
                        <div className="flex justify-between items-center">
                          <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                            Total Amount
                          </h4>
                          <p className="text-2xl font-bold text-blue-600">
                            {result.total} {result.currency}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
